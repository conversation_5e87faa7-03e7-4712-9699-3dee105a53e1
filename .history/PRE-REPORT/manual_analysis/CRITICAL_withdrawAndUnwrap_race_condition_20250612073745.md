# CRITICAL VULNERABILITY: Race Condition in withdrawAndUnwrap Function

**Severity**: CRITICAL  
**Contract**: HoldingManager.sol  
**Function**: withdrawAndUnwrap  
**Lines**: 224-238  
**Impact**: Protocol Insolvency, Bad Debt Creation  

## Executive Summary

The `withdrawAndUnwrap` function in HoldingManager contains a critical race condition that allows users to withdraw WETH collateral while bypassing solvency checks. This vulnerability could lead to protocol insolvency and bad debt creation, potentially draining significant funds from the protocol.

## Vulnerability Details

### Root Cause
The function performs operations in the wrong order:
1. **Line 227**: Transfers WETH from user's holding to HoldingManager contract
2. **Line 228**: Unwraps WETH to ETH
3. **Line 229**: Calls `_withdraw()` which performs solvency check

**The Problem**: The WETH tokens are moved out of the holding contract BEFORE the solvency check occurs, creating a window where the holding's token balance is reduced but collateral accounting may still reflect the old values.

### Code Analysis

```solidity
function withdrawAndUnwrap(uint256 _amount) external override validAmount(_amount) validHolding(userHolding[msg.sender]) nonReentrant whenNotPaused {
    IHolding(userHolding[msg.sender]).transfer({ _token: WETH, _to: address(this), _amount: _amount }); // ❌ TOKENS MOVED FIRST
    _unwrap(_amount);
    (uint256 userAmount, uint256 feeAmount) = _withdraw({ _token: WETH, _amount: _amount }); // ❌ SOLVENCY CHECK AFTER

    if (feeAmount > 0) {
        (bool feeSuccess,) = payable(manager.feeAddress()).call{ value: feeAmount }("");
        require(feeSuccess, "3016");
    }

    (bool success,) = payable(msg.sender).call{ value: userAmount }("");
    require(success, "3016");
}
```

### Solvency Check Flow
In `_withdraw()`:
```solidity
function _withdraw(address _token, uint256 _amount) private returns (uint256, uint256) {
    require(manager.isTokenWithdrawable(_token), "3071");
    address holding = userHolding[msg.sender];

    // Check if token has collateral registry
    (, address _tokenRegistry) = _getStablesManager().shareRegistryInfo(_token);
    if (_tokenRegistry != address(0) && ISharesRegistry(_tokenRegistry).collateral(holding) > 0) {
        _getStablesManager().removeCollateral({ _holding: holding, _token: _token, _amount: _amount }); // ❌ SOLVENCY CHECK INSIDE
    }
    // ... fee calculation
}
```

In `StablesManager.removeCollateral()`:
```solidity
function removeCollateral(address _holding, address _token, uint256 _amount) external override onlyAllowed whenNotPaused {
    require(shareRegistryInfo[_token].active, "1201");

    emit RemovedCollateral({ holding: _holding, token: _token, amount: _amount });
    _getRegistry(_token).unregisterCollateral({ _holding: _holding, _share: _amount }); // ❌ COLLATERAL REMOVED FIRST
    require(isSolvent({ _token: _token, _holding: _holding }), "3009"); // ❌ SOLVENCY CHECK AFTER REMOVAL
}
```

## Attack Scenario

### Setup
- Attacker has a holding with:
  - 1000 WETH collateral (worth $2M at $2000/ETH)
  - 1.8M jUSD debt (90% collateralized, close to liquidation threshold)

### Attack Execution
1. Attacker calls `withdrawAndUnwrap(1000 WETH)`
2. `holding.transfer()` moves 1000 WETH from holding to HoldingManager
   - Holding now has 0 WETH balance
   - Collateral accounting still shows 1000 WETH (not updated yet)
3. `_unwrap()` converts WETH to ETH in HoldingManager
4. `_withdraw()` is called:
   - Checks `ISharesRegistry(_tokenRegistry).collateral(holding) > 0` → returns true (1000 WETH)
   - Calls `removeCollateral()` which updates accounting to 0 WETH
   - `isSolvent()` check happens with 0 WETH collateral vs 1.8M jUSD debt
   - **Should fail, but tokens are already transferred out!**

### Result
- Attacker receives 1000 WETH (minus fees) as ETH
- Holding has 1.8M jUSD debt but 0 WETH collateral
- Protocol has bad debt of 1.8M jUSD

## Impact Assessment

### Financial Impact
- **Severity**: CRITICAL
- **Funds at Risk**: All collateral in positions near liquidation threshold
- **Attack Cost**: Only gas fees
- **Profit Potential**: High - attacker extracts collateral while keeping debt

### Technical Impact
- **Protocol Solvency**: Compromised through bad debt creation
- **User Impact**: Other users may face liquidation due to protocol insolvency
- **System Stability**: High risk of protocol failure and bank run

### Economic Impact
- **Market Confidence**: Severe damage to protocol reputation
- **Liquidation Cascade**: Bad debt could trigger mass liquidations
- **Recovery Cost**: Protocol may need emergency funding to cover bad debt

## Proof of Concept

### Test Case Structure
```solidity
function test_withdrawAndUnwrap_solvency_bypass() public {
    // Setup user with WETH collateral at 90% collateralization
    uint256 collateralAmount = 1000e18; // 1000 WETH
    uint256 borrowAmount = 1800e18; // 1800 jUSD (90% of $2M collateral)
    
    // User deposits WETH and borrows jUSD
    vm.startPrank(user);
    holdingManager.deposit(WETH, collateralAmount);
    holdingManager.borrow(WETH, borrowAmount, 0, false);
    
    // Verify user is solvent but close to liquidation
    assertTrue(stablesManager.isSolvent(WETH, userHolding));
    
    // Attempt to withdraw all WETH - should fail but may succeed
    holdingManager.withdrawAndUnwrap(collateralAmount);
    
    // Verify bad debt creation
    assertEq(IERC20(WETH).balanceOf(userHolding), 0); // No WETH left
    assertGt(sharesRegistry.borrowed(userHolding), 0); // Still has debt
    assertFalse(stablesManager.isSolvent(WETH, userHolding)); // Now insolvent
}
```

## Recommended Fix

### Solution: Reorder Operations
```solidity
function withdrawAndUnwrap(uint256 _amount) external override validAmount(_amount) validHolding(userHolding[msg.sender]) nonReentrant whenNotPaused {
    // ✅ FIRST: Perform solvency check and collateral removal
    (uint256 userAmount, uint256 feeAmount) = _withdraw({ _token: WETH, _amount: _amount });
    
    // ✅ THEN: Transfer tokens and unwrap (after solvency is confirmed)
    IHolding(userHolding[msg.sender]).transfer({ _token: WETH, _to: address(this), _amount: _amount });
    _unwrap(_amount);

    // ✅ FINALLY: Distribute ETH
    if (feeAmount > 0) {
        (bool feeSuccess,) = payable(manager.feeAddress()).call{ value: feeAmount }("");
        require(feeSuccess, "3016");
    }

    (bool success,) = payable(msg.sender).call{ value: userAmount }("");
    require(success, "3016");
}
```

### Alternative Solution: Enhanced Validation
Add explicit solvency check before any token movement:
```solidity
function withdrawAndUnwrap(uint256 _amount) external override validAmount(_amount) validHolding(userHolding[msg.sender]) nonReentrant whenNotPaused {
    address holding = userHolding[msg.sender];
    
    // ✅ Pre-validate solvency before any state changes
    require(stablesManager.wouldRemainSolventAfterWithdrawal(WETH, holding, _amount), "Would become insolvent");
    
    // Continue with existing flow...
    IHolding(holding).transfer({ _token: WETH, _to: address(this), _amount: _amount });
    _unwrap(_amount);
    (uint256 userAmount, uint256 feeAmount) = _withdraw({ _token: WETH, _amount: _amount });
    
    // ... rest of function
}
```

## Verification Steps

1. **Code Review**: Verify the fix maintains the same functionality while eliminating the race condition
2. **Unit Tests**: Implement comprehensive test cases covering edge cases
3. **Integration Tests**: Test interaction with other protocol components
4. **Formal Verification**: Consider formal verification of critical properties
5. **Security Audit**: Have the fix reviewed by external security experts

## Timeline

- **Immediate**: Stop any production deployment until fixed
- **Within 24 hours**: Implement and test the fix
- **Within 48 hours**: Deploy fix to testnet and conduct thorough testing
- **Within 72 hours**: Deploy fix to mainnet after security review

This vulnerability represents a critical threat to protocol solvency and requires immediate attention.
